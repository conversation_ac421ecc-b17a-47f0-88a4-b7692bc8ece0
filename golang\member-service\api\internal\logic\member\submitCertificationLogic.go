package member

import (
	"context"
	"fmt"
	"member/member"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type SubmitCertificationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 提交实名认证
func NewSubmitCertificationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubmitCertificationLogic {
	return &SubmitCertificationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SubmitCertificationLogic) SubmitCertification(req *types.SubmitCertificationReq) (resp *types.CommonResp, err error) {

	var memberId int64
	// 从JWT token 中获取当前用户ID
	memberIdvalue := l.ctx.Value("member_id")
	if memberIdvalue == nil {
		return nil, fmt.Errorf("无法获取用户身份信息")
	}

	// 开始处理不通的类型对应member_id不同的类型
	switch v := memberIdvalue.(type) {
	case int64:
		memberId = v
	case float64:
		memberId = int64(v)
	case string:
		if parsed, parseErr := strconv.ParseInt(v, 10, 64); parseErr == nil {
			memberId = parsed
		} else {
			return nil, fmt.Errorf("用户身份信息格式有误")
		}
	default:
		return nil, fmt.Errorf("用户身份信息类型错误：%T", v)
	}

	// 调用RPC服务开始处理业务功能
	rpcResp, err := l.svcCtx.MemberRpc.SubmitCertification(l.ctx, &member.SubmitCertificationReq{
		MemberId:          memberId,
		Realname:          req.Realname,
		IdentityCard:      req.IdentityCard,
		IdentityCardFront: req.IdentityCardFront,
		IdentityCardBack:  req.IdentityCardBack,
		Address:           req.Address,
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC获取用户信息失败: %v", err)
	}

	if rpcResp.Code != 200 {
		return &types.CommonResp{
			Code:    int(rpcResp.Code),
			Message: rpcResp.Message,
		}, nil
	}

	return &types.CommonResp{
		Code:    int(rpcResp.Code),
		Message: rpcResp.Message,
	}, nil
}
