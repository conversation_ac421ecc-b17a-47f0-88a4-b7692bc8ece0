package member

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SubmitCancelApplicationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 提交注销申请
func NewSubmitCancelApplicationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubmitCancelApplicationLogic {
	return &SubmitCancelApplicationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SubmitCancelApplicationLogic) SubmitCancelApplication(req *types.SubmitCancelReq) (resp *types.CommonResp, err error) {
	// todo: add your logic here and delete this line

	return
}
