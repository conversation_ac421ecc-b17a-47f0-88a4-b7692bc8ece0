package model

import (
	"errors"
	"fmt"

	"gorm.io/gorm"
)

// MemberCertification 实名认证
type MemberCertification struct {
	ID           int64   `gorm:"primaryKey;autoIncrement;column:id"`
	MerchantID   int64   `gorm:"column:merchant_id"`
	StoreID      int64   `gorm:"column:store_id"`
	MemberID     int64   `gorm:"column:member_id;index"`
	MemberType   int32   `gorm:"column:member_type"`
	Realname     string  `gorm:"column:realname"`
	IdentityCard string  `gorm:"column:identity_card"`
	FrontImage   string  `gorm:"column:identity_card_front"`
	BackImage    string  `gorm:"column:identity_card_back"`
	Gender       string  `gorm:"column:gender"`
	Birthday     *string `gorm:"column:birthday"`
	FrontIsFake  int32   `gorm:"column:front_is_fake"`
	BackIsFake   int32   `gorm:"column:back_is_fake"`
	Nationality  string  `gorm:"column:nationality"`
	Address      string  `gorm:"column:address"`
	StartDate    *string `gorm:"column:start_date"`
	EndDate      *string `gorm:"column:end_date"`
	Issue        string  `gorm:"column:issue"`
	IsSelf       int32   `gorm:"column:is_self"`
	Status       int32   `gorm:"column:status"`
	CreatedAt    int64   `gorm:"column:created_at"`
	UpdatedAt    int64   `gorm:"column:updated_at"`
}

func (MemberCertification) TableName() string { return "member_certification" }

type MemberCertificationModel struct{ db *gorm.DB }

func NewMemberCertificationModel(db *gorm.DB) *MemberCertificationModel {
	return &MemberCertificationModel{db: db}
}

func (m *MemberCertificationModel) FindPending(page, pageSize int) ([]MemberCertification, int64, error) {
	var list []MemberCertification
	var total int64
	q := m.db.Model(&MemberCertification{}).Where("status = ?", 0)
	if err := q.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	if err := q.Limit(pageSize).Offset((page - 1) * pageSize).Order("id desc").Find(&list).Error; err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (m *MemberCertificationModel) FindByID(id int64) (*MemberCertification, error) {
	var data MemberCertification
	if err := m.db.First(&data, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("认证ID=%d不存在", id)
		}
		return nil, err
	}
	return &data, nil
}

func (m *MemberCertificationModel) FindByMemberID(memberID int64) (*MemberCertification, error) {
	var data MemberCertification
	if err := m.db.Where("member_id=?", memberID).First(&data).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("会员ID=%d的认证不存在", memberID)
		}
		return nil, err
	}
	return &data, nil
}

func (m *MemberCertificationModel) Create(data *MemberCertification) error {
	return m.db.Create(data).Error
}

func (m *MemberCertificationModel) Update(data *MemberCertification) error {
	return m.db.Save(data).Error
}
