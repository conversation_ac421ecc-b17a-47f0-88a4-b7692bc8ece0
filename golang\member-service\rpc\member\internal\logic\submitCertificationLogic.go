package logic

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"member/internal/svc"
	"member/member"
	"member/model"
)

type SubmitCertificationLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSubmitCertificationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubmitCertificationLogic {
	return &SubmitCertificationLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *SubmitCertificationLogic) SubmitCertification(in *member.SubmitCertificationReq) (*member.CommonResp, error) {

	// 参数校验
	if in.MemberId <= 0 {
		return nil, fmt.Errorf("用户身份校验失败,请重新上传文件")
	}

	// 开始处理用户上传的数据到数据中
	err := l.svcCtx.MemberCertificationModel.Update(&model.MemberCertification{
		MemberID:     in.MemberId,
		Realname:     in.Realname,
		IdentityCard: in.IdentityCard,
		FrontImage:   in.IdentityCardBack,
		BackImage:    in.IdentityCardBack,
		Address:      in.Address,
	})
	if err != nil {
		return &member.CommonResp{
			Code:    500,
			Message: "提交实名认证信息失败,请稍后重新尝试",
		}, nil
	}
	return &member.CommonResp{
		Code:    200,
		Message: "提交实名认证信息成功",
	}, nil
}
