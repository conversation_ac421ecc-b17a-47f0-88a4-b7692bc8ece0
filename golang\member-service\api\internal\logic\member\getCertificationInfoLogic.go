package member

import (
	"context"
	"fmt"
	"member/member"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type GetCertificationInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取实名认证信息
func NewGetCertificationInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCertificationInfoLogic {
	return &GetCertificationInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCertificationInfoLogic) GetCertificationInfo(req *types.GetCertificationInfoReq) (resp *types.GetCertificationInfoResp, err error) {
	var memberId int64
	//  从JWT token 中获取当前用户ID
	memberIdvalue := l.ctx.Value("member_id")
	if memberIdvalue == nil {
		return nil, fmt.Errorf("无法获取用户身份信息")
	}
	// 开始处理不同的类型的member_id的值
	switch v := memberIdvalue.(type) {
	case int64:
		memberId = v
	case float64:
		memberId = int64(v)
	case string:
		if parsed, parseErr := strconv.ParseInt(v, 10, 64); parseErr == nil {
			memberId = parsed
		} else {
			return nil, fmt.Errorf("用户身份信息格式错误")
		}
	default:
		return nil, fmt.Errorf("用户身份信息类型错误: %T", v)
	}

	if memberId <= 0 {
		return nil, fmt.Errorf("用户ID无效")
	}

	// 开始调用RPC服务开始处理业务
	rpcResp, err := l.svcCtx.MemberRpc.GetCertificationInfo(l.ctx, &member.GetCertificationInfoReq{
		MemberId: memberId,
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC获取实名认证信息失败: %v", err)
	}

	// 检查RPC响应码
	if rpcResp.Code != 200 {
		return &types.GetCertificationInfoResp{
			Code:    int(rpcResp.Code),
			Message: rpcResp.Message,
		}, nil
	}
	return &types.GetCertificationInfoResp{
		Code:    200,
		Message: "获取实名认证信息成功",
		Data: types.MemberCertification{
			Id:                rpcResp.Data.Id,
			MerchantId:        rpcResp.Data.MerchantId,
			StoreId:           rpcResp.Data.StoreId,
			MemberId:          rpcResp.Data.MemberId,
			MemberType:        int(rpcResp.Data.MemberType),
			Realname:          rpcResp.Data.Realname,
			IdentityCard:      rpcResp.Data.IdentityCard,
			IdentityCardFront: rpcResp.Data.IdentityCardFront,
			IdentityCardBack:  rpcResp.Data.IdentityCardBack,
			Gender:            rpcResp.Data.Gender,
			Birthday:          rpcResp.Data.Birthday,
			FrontIsFake:       int(rpcResp.Data.FrontIsFake),
			BackIsFake:        int(rpcResp.Data.BackIsFake),
			Nationality:       rpcResp.Data.Nationality,
			Address:           rpcResp.Data.Address,
			StartDate:         rpcResp.Data.StartDate,
			EndDate:           rpcResp.Data.EndDate,
			Issue:             rpcResp.Data.Issue,
			IsSelf:            int(rpcResp.Data.IsSelf),
			Status:            int(rpcResp.Data.Status),
			CreatedAt:         rpcResp.Data.CreatedAt,
			UpdatedAt:         rpcResp.Data.UpdatedAt,
		},
	}, nil
}
