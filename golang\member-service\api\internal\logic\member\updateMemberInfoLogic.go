package member

import (
	"context"
	"fmt"
	memberpb "member/member"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type UpdateMemberInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新用户信息
func NewUpdateMemberInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateMemberInfoLogic {
	return &UpdateMemberInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateMemberInfoLogic) UpdateMemberInfo(req *types.UpdateMemberInfoReq) (resp *types.CommonResp, err error) {

	// 从JWT token 中获取当前用户ID
	memberId := l.ctx.Value("member_id")
	memberIdInt64, ok := memberId.(int64)
	if !ok {
		return nil, fmt.Errorf("无效的用户ID")
	}
	// 调用RPC服务获取用户信息
	rpcResp, err := l.svcCtx.MemberRpc.UpdateMemberInfo(l.ctx, &memberpb.UpdateMemberInfoReq{
		MemberId:     memberIdInt64,
		Realname:     req.Realname,
		Nickname:     req.Nickname,
		HeadPortrait: req.HeadPortrait,
		Gender:       int32(req.Gender),
		Qq:           req.Qq,
		Email:        req.Email,
		Birthday:     req.Birthday,
		ProvinceId:   req.ProvinceId,
		CityId:       req.CityId,
		AreaId:       req.AreaId,
		Address:      req.Address,
		TelNo:        req.TelNo,
		BgImage:      req.BgImage,
		Description:  req.Description,
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC更新用户失败: %v", err)
	}

	// 检查RPC响应是否正确
	if rpcResp.Code != 200 {
		return &types.CommonResp{
			Code:    int(rpcResp.Code),
			Message: rpcResp.Message,
		}, nil
	}

	return &types.CommonResp{
		Code:    200,
		Message: "更新用户信息成功",
	}, nil
}
