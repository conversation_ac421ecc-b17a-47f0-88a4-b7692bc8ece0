package member

import (
	"context"
	"fmt"
	memberpb "member/member"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type UpdateMemberInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新用户信息
func NewUpdateMemberInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateMemberInfoLogic {
	return &UpdateMemberInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateMemberInfoLogic) UpdateMemberInfo(req *types.UpdateMemberInfoReq) (resp *types.CommonResp, err error) {

	// 从JWT token 中获取当前用户ID
	var memberIdInt64 int64
	memberIdvalue := l.ctx.Value("member_id")
	if memberIdvalue == nil {
		return nil, fmt.Errorf("无法获取用户身份信息")
	}

	// 开始处理不同类型的member_id的值
	switch v := memberIdvalue.(type) {
	case int64:
		memberIdInt64 = v
	case float64:
		memberIdInt64 = int64(v)
	case string:
		if parsed, parseErr := strconv.ParseInt(v, 10, 64); parseErr == nil {
			memberIdInt64 = parsed
		} else {
			return nil, fmt.Errorf("用户身份信息格式错误")
		}
	default:
		return nil, fmt.Errorf("用户身份信息类型错误: %T", v)
	}

	if memberIdInt64 <= 0 {
		return nil, fmt.Errorf("用户ID无效")
	}
	// 调用RPC服务获取用户信息
	rpcResp, err := l.svcCtx.MemberRpc.UpdateMemberInfo(l.ctx, &memberpb.UpdateMemberInfoReq{
		MemberId:     memberIdInt64,
		Realname:     req.Realname,
		Nickname:     req.Nickname,
		HeadPortrait: req.HeadPortrait,
		Gender:       int32(req.Gender),
		Qq:           req.Qq,
		Email:        req.Email,
		Birthday:     req.Birthday,
		ProvinceId:   req.ProvinceId,
		CityId:       req.CityId,
		AreaId:       req.AreaId,
		Address:      req.Address,
		TelNo:        req.TelNo,
		BgImage:      req.BgImage,
		Description:  req.Description,
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC更新用户失败: %v", err)
	}

	// 检查RPC响应是否正确
	if rpcResp.Code != 200 {
		return &types.CommonResp{
			Code:    int(rpcResp.Code),
			Message: rpcResp.Message,
		}, nil
	}

	return &types.CommonResp{
		Code:    200,
		Message: "更新用户信息成功",
	}, nil
}
