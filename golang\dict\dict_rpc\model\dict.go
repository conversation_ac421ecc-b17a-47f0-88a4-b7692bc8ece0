package model

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"gorm.io/gorm"
)

// Dict 字典模型
type Dict struct {
	ID          int64          `gorm:"primaryKey;autoIncrement"`
	Code        string         `gorm:"type:varchar(50);uniqueIndex;not null"`
	Name        string         `gorm:"type:varchar(100);not null"`
	Remark      string         `gorm:"type:varchar(500)"`
	Status      int32          `gorm:"type:tinyint;not null;default:1;comment:'状态 0:禁用 1:启用'"`
	CreatedTime time.Time      `gorm:"not null;autoCreateTime"`
	UpdatedTime time.Time      `gorm:"not null;autoUpdateTime"`
	DeletedAt   gorm.DeletedAt `gorm:"index"`
}

// TableName 设置表名
func (Dict) TableName() string {
	return "dict"
}




// DictModel 字典模型结构
type DictModel struct {
	db *gorm.DB
}

// NewDictModel 创建字典模型
func NewDictModel(db *gorm.DB) *DictModel {
	return &DictModel{
		db: db,
	}
}

// FindByID 根据ID查询字典
func (m *DictModel) FindByID(id int64) (*Dict, error) {
	var dict Dict
	result := m.db.First(&dict, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("字典id=" + strconv.Itoa(int(id)) + "不存在")
		}
		return nil, result.Error
	}
	return &dict, nil
}

// FindByCode 根据编码查询字典
func (m *DictModel) FindByCode(code string) (*Dict, error) {
	var dict Dict
	result := m.db.Where("code = ?", code).First(&dict)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("字典编码=" + code + "不存在")
		}
		return nil, result.Error
	}
	return &dict, nil
}

// FindByName 根据名称查询字典
func (m *DictModel) FindByName(name string) (*Dict, error) {
	var dict Dict
	result := m.db.Where("name = ?", name).First(&dict)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("字典名称=" + name + "不存在")
		}
		return nil, result.Error
	}
	return &dict, nil
}

// Create 创建字典
func (m *DictModel) Create(dict *Dict) error {
	return m.db.Create(dict).Error
}

// Update 更新字典项
func (m *DictModel) Update(dict *Dict) error {
	if dict.ID <= 0 {
		return fmt.Errorf("字典ID不能为空或小于等于0")
	}

	// 先检查记录是否存在
	var existing Dict
	if err := m.db.First(&existing, dict.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("字典ID=%d不存在", dict.ID)
		}
		return err
	}

	// 更新时间戳
	dict.UpdatedTime = time.Now()

	return m.db.Save(dict).Error
}

// Delete 删除字典
func (m *DictModel) Delete(id int64) error {
	return m.db.Delete(&Dict{}, id).Error
}

// List 分页查询字典列表
func (m *DictModel) List(page, pageSize int32) ([]*Dict, int64, error) {
	var dicts []*Dict
	var total int64

	query := m.db.Model(&Dict{})

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := int((page - 1) * pageSize)
	if err := query.Offset(offset).Limit(int(pageSize)).Order("created_time DESC").Find(&dicts).Error; err != nil {
		return nil, 0, err
	}

	return dicts, total, nil
}

// CheckCodeExists 检查编码是否存在
func (m *DictModel) CheckCodeExists(code string, excludeID int64) (bool, error) {
	var count int64
	query := m.db.Model(&Dict{}).Where("code = ?", code)
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}
	if err := query.Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

// 根据ID获取字典及其分类和项目（带排序）
func (m *DictModel) GetWithCategoriesAndItems(db *gorm.DB, id int64) error {
	return db.Preload("Categories", "status = 1", func(db *gorm.DB) *gorm.DB {
		return db.Order("sort ASC")
	}).Preload("Items", "status = 1", func(db *gorm.DB) *gorm.DB {
		return db.Order("sort ASC")
	}).Where("id = ?", id).First(m).Error
}

// DeleteWithCascade 级联删除字典及其相关的分类和项目
func (m *DictModel) DeleteWithCascade(id int64) error {
	return m.db.Transaction(func(tx *gorm.DB) error {
		// 1. 删除字典项 (dict_item表)
		if err := tx.Where("dict_id = ?", id).Delete(&DictItem{}).Error; err != nil {
			return err
		}

		// 2. 删除字典分类 (dict_category表)
		if err := tx.Where("dict_id = ?", id).Delete(&DictCategory{}).Error; err != nil {
			return err
		}

		// 3. 删除字典本身
		if err := tx.Delete(&Dict{}, id).Error; err != nil {
			return err
		}

		return nil
	})
}

// DictCategory 字典分类模型 (用于级联删除)
type DictCategory struct {
	ID          int64          `gorm:"primaryKey;autoIncrement"`
	DictID      int64          `gorm:"not null;index"`
	Name        string         `gorm:"type:varchar(100);not null"`
	Code        string         `gorm:"type:varchar(50);not null"`
	Sort        int32          `gorm:"type:int;not null;default:0"`
	Status      int32          `gorm:"type:tinyint;not null;default:1;comment:'状态 0:禁用 1:启用'"`
	CreatedTime time.Time      `gorm:"not null;autoCreateTime"`
	UpdatedTime time.Time      `gorm:"not null;autoUpdateTime"`
	DeletedAt   gorm.DeletedAt `gorm:"index"`
}

// TableName 设置表名
func (DictCategory) TableName() string {
	return "dict_category"
}

// DictItem 字典项模型 (用于级联删除)
type DictItem struct {
	ID          int64          `gorm:"primaryKey;autoIncrement"`
	DictID      int64          `gorm:"not null;index"`
	CategoryID  int64          `gorm:"not null;index"`
	Code        string         `gorm:"type:varchar(50);not null"`
	Name        string         `gorm:"type:varchar(100);not null"`
	Sort        int32          `gorm:"type:int;not null;default:0"`
	Status      int32          `gorm:"type:tinyint;not null;default:1;comment:'状态 0:禁用 1:启用'"`
	CreatedTime time.Time      `gorm:"not null;autoCreateTime"`
	UpdatedTime time.Time      `gorm:"not null;autoUpdateTime"`
	DeletedAt   gorm.DeletedAt `gorm:"index"`
}

// TableName 设置表名
func (DictItem) TableName() string {
	return "dict_item"
}
