package member

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetCancelStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取注销状态
func NewGetCancelStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCancelStatusLogic {
	return &GetCancelStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCancelStatusLogic) GetCancelStatus(req *types.GetCancelStatusReq) (resp *types.GetCancelStatusResp, err error) {
	// todo: add your logic here and delete this line

	return
}
