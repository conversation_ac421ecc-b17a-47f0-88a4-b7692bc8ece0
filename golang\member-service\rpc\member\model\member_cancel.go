package model

import (
	"errors"
	"fmt"

	"gorm.io/gorm"
)

// MemberCancel 注销申请
type MemberCancel struct {
	ID           int64  `gorm:"primaryKey;autoIncrement;column:id"`
	MerchantID   int64  `gorm:"column:merchant_id"`
	StoreID      int64  `gorm:"column:store_id"`
	MemberID     int64  `gorm:"column:member_id;index"`
	Content      string `gorm:"column:content;type:text"`
	AuditStatus  int32  `gorm:"column:audit_status"`
	AuditTime    int64  `gorm:"column:audit_time"`
	RefusalCause string `gorm:"column:refusal_cause"`
	IsAddon      int32  `gorm:"column:is_addon"`
	AddonName    string `gorm:"column:addon_name"`
	Status       int32  `gorm:"column:status"`
	CreatedAt    int64  `gorm:"column:created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at"`
}

func (MemberCancel) TableName() string { return "member_cancel" }

type MemberCancelModel struct{ db *gorm.DB }

func NewMemberCancelModel(db *gorm.DB) *MemberCancelModel { return &MemberCancelModel{db: db} }

func (m *MemberCancelModel) FindPending(page, pageSize int) ([]MemberCancel, int64, error) {
	var list []MemberCancel
	var total int64
	q := m.db.Model(&MemberCancel{}).Where("audit_status = ?", 0)
	if err := q.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	if err := q.Limit(pageSize).Offset((page - 1) * pageSize).Order("id desc").Find(&list).Error; err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (m *MemberCancelModel) FindByID(id int64) (*MemberCancel, error) {
	var data MemberCancel
	if err := m.db.First(&data, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("注销申请ID=%d不存在", id)
		}
		return nil, err
	}
	return &data, nil
}

func (m *MemberCancelModel) FindByMemberID(memberID int64) (*MemberCancel, error) {
	var data MemberCancel
	if err := m.db.Where("member_id=?", memberID).First(&data).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("会员ID=%d的注销申请不存在", memberID)
		}
		return nil, err
	}
	return &data, nil
}
