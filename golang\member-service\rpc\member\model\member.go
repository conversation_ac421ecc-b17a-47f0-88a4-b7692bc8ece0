package model

import (
	"errors"
	"fmt"
	"time"

	"github.com/duke-git/lancet/v2/cryptor"
	"gorm.io/gorm"
)

// Member 会员主表
// 对应表: member
type Member struct {
	ID                 int64   `gorm:"primaryKey;autoIncrement;column:id"`
	MerchantID         int64   `gorm:"column:merchant_id"`
	StoreID            int64   `gorm:"column:store_id"`
	Username           string  `gorm:"column:username;type:varchar(20);not null"`
	PasswordHash       string  `gorm:"column:password_hash;type:varchar(150);not null"`
	AuthKey            string  `gorm:"column:auth_key;type:varchar(32)"`
	PasswordResetToken string  `gorm:"column:password_reset_token;type:varchar(150)"`
	MobileResetToken   string  `gorm:"column:mobile_reset_token;type:varchar(150)"`
	Type               int32   `gorm:"column:type"`
	Realname           string  `gorm:"column:realname;type:varchar(50)"`
	Nickname           string  `gorm:"column:nickname;type:varchar(60)"`
	HeadPortrait       string  `gorm:"column:head_portrait;type:varchar(150)"`
	Gender             int32   `gorm:"column:gender"`
	QQ                 string  `gorm:"column:qq;type:varchar(20)"`
	Email              string  `gorm:"column:email;type:varchar(60)"`
	Birthday           *string `gorm:"column:birthday"`
	ProvinceID         int64   `gorm:"column:province_id"`
	CityID             int64   `gorm:"column:city_id"`
	AreaID             int64   `gorm:"column:area_id"`
	Address            string  `gorm:"column:address;type:varchar(100)"`
	Mobile             string  `gorm:"column:mobile;type:varchar(20)"`
	TelNo              string  `gorm:"column:tel_no;type:varchar(20)"`
	BGImage            string  `gorm:"column:bg_image;type:varchar(200)"`
	Description        string  `gorm:"column:description;type:varchar(200)"`
	VisitCount         int32   `gorm:"column:visit_count"`
	LastTime           int64   `gorm:"column:last_time"`
	LastIP             string  `gorm:"column:last_ip;type:varchar(40)"`
	Role               int32   `gorm:"column:role"`
	CurrentLevel       int32   `gorm:"column:current_level"`
	LevelExpireTime    int64   `gorm:"column:level_expiration_time"`
	LevelBuyType       int32   `gorm:"column:level_buy_type"`
	PID                int64   `gorm:"column:pid"`
	Level              int32   `gorm:"column:level"`
	Tree               string  `gorm:"column:tree;type:varchar(2000)"`
	PromoterCode       string  `gorm:"column:promoter_code;type:varchar(50)"`
	CertificationType  int32   `gorm:"column:certification_type"`
	Source             string  `gorm:"column:source;type:varchar(50)"`
	Status             int32   `gorm:"column:status"`
	CreatedAt          int64   `gorm:"column:created_at"`
	UpdatedAt          int64   `gorm:"column:updated_at"`
	RegionID           int64   `gorm:"column:region_id"`
}

func (Member) TableName() string { return "member" }

// MemberModel 成员模型封装
type MemberModel struct {
	db *gorm.DB
}

func NewMemberModel(db *gorm.DB) *MemberModel { return &MemberModel{db: db} }

func (m *MemberModel) FindByID(id int64) (*Member, error) {
	var data Member
	if err := m.db.First(&data, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("用户id=%d不存在", id)
		}
		return nil, err
	}
	return &data, nil
}

func (m *MemberModel) FindByUsername(username string) (*Member, error) {
	var data Member
	if err := m.db.Where("username = ?", username).First(&data).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("用户名=%s不存在", username)
		}
		return nil, err
	}
	return &data, nil
}

func (m *MemberModel) FindByMobile(mobile string) (*Member, error) {
	var data Member
	if err := m.db.Where("mobile = ?", mobile).First(&data).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("手机号=%s不存在", mobile)
		}
		return nil, err
	}
	return &data, nil
}

func (m *MemberModel) Create(data *Member, key string) error {
	// 先对用户的密码进行加密处理
	hashPassword := cryptor.HmacSha256(data.PasswordHash, key)
	data.PasswordHash = hashPassword

	// 设置时间戳
	now := time.Now().Unix()
	data.CreatedAt = now
	data.UpdatedAt = now

	return m.db.Create(data).Error
}

func (m *MemberModel) Update(id int64, data *Member) error {
	if id <= 0 {
		return fmt.Errorf("ID不能为空或小于等于0")
	}

	// 确保传入的数据包含正确的ID
	data.ID = id

	// 先检查记录是否存在
	var existing Member
	if err := m.db.First(&existing, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("会员ID=%d不存在", id)
		}
		return err
	}

	// 更新时间戳
	data.UpdatedAt = time.Now().Unix()

	return m.db.Save(data).Error
}

func (m *MemberModel) UpdatePassword(data *Member, key string) error {
	//  先对用户的密码进行加密处理
	hashPassword := cryptor.HmacSha256(data.PasswordHash, key)
	data.PasswordHash = hashPassword
	if err := m.db.Model(&Member{}).Where("mobile=?", data.Mobile).Update("password_hash", hashPassword).Error; err != nil {
		return fmt.Errorf("更新%s的密码失败", data.Username)
	}
	return m.db.Save(data).Error
}
