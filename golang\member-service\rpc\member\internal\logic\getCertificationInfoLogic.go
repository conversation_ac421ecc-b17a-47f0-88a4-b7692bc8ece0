package logic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"member/internal/svc"
	"member/member"
)

type GetCertificationInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetCertificationInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCertificationInfoLogic {
	return &GetCertificationInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 实名认证管理
func (l *GetCertificationInfoLogic) GetCertificationInfo(in *member.GetCertificationInfoReq) (*member.GetCertificationInfoResp, error) {

	if in.MemberId <= 0 {
		return &member.GetCertificationInfoResp{
			Code:    400,
			Message: "用户ID不能为空",
		}, nil
	}

	// 调用数据库开始查询认证信息
	certificationData, err := l.svcCtx.MemberCertificationModel.FindByMemberID(in.MemberId)

	if err != nil {
		return &member.GetCertificationInfoResp{
			Code:    500,
			Message: "查询认证信息失败",
		}, nil
	}

	return &member.GetCertificationInfoResp{
		Code:    200,
		Message: "查询认证信息成功",
		Data: &member.MemberCertification{
			Id:                certificationData.ID,
			MemberId:          certificationData.MerchantID,
			Realname:          certificationData.Realname,
			IdentityCard:      certificationData.IdentityCard,
			IdentityCardFront: certificationData.FrontImage,
			IdentityCardBack:  certificationData.BackImage,
			Gender:            certificationData.Gender,
			Birthday:          *certificationData.Birthday,
			FrontIsFake:       certificationData.FrontIsFake,
			BackIsFake:        certificationData.BackIsFake,
			Nationality:       certificationData.Nationality,
			Address:           certificationData.Address,
			StartDate:         *certificationData.StartDate,
			Issue:             certificationData.Issue,
			IsSelf:            certificationData.IsSelf,
			Status:            certificationData.Status,
			CreatedAt:         certificationData.CreatedAt,
			UpdatedAt:         certificationData.UpdatedAt,
		},
	}, nil
}
