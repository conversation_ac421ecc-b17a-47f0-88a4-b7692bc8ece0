package model

import (
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// MemberStat 统计表
type MemberStat struct {
	ID           int64 `gorm:"primaryKey;autoIncrement;column:id"`
	MerchantID   int64 `gorm:"column:merchant_id"`
	StoreID      int64 `gorm:"column:store_id"`
	MemberID     int64 `gorm:"column:member_id;index"`
	MemberType   int32 `gorm:"column:member_type"`
	NiceNum      int64 `gorm:"column:nice_num"`
	DisagreeNum  int64 `gorm:"column:disagree_num"`
	TransmitNum  int64 `gorm:"column:transmit_num"`
	CommentNum   int64 `gorm:"column:comment_num"`
	CollectNum   int64 `gorm:"column:collect_num"`
	ReportNum    int64 `gorm:"column:report_num"`
	RecommendNum int64 `gorm:"column:recommend_num"`
	FollowNum    int64 `gorm:"column:follow_num"`
	AllowedNum   int64 `gorm:"column:allowed_num"`
	View         int64 `gorm:"column:view"`
	Status       int32 `gorm:"column:status"`
	CreatedAt    int64 `gorm:"column:created_at"`
	UpdatedAt    int64 `gorm:"column:updated_at"`
}

func (MemberStat) TableName() string { return "member_stat" }

type MemberStatModel struct{ db *gorm.DB }

func NewMemberStatModel(db *gorm.DB) *MemberStatModel { return &MemberStatModel{db: db} }

func (m *MemberStatModel) FindByMemberID(memberID int64) (*MemberStat, error) {
	var data MemberStat
	if err := m.db.Where("member_id=?", memberID).First(&data).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("会员ID=%d的统计不存在", memberID)
		}
		return nil, err
	}
	return &data, nil
}

func (m *MemberStatModel) Create(data *MemberStat) error { return m.db.Create(data).Error }

func (m *MemberStatModel) Update(id int64, data *MemberStat) error {
	if id <= 0 {
		return fmt.Errorf("ID不能为空或小于等于0")
	}

	// 确保传入的数据包含正确的ID
	data.ID = id

	// 先检查记录是否存在
	var existing MemberStat
	if err := m.db.First(&existing, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("统计ID=%d不存在", id)
		}
		return err
	}

	// 更新时间戳
	data.UpdatedAt = time.Now().Unix()

	return m.db.Save(data).Error
}

func (m *MemberStatModel) IncreaseView(memberID int64, step int64) error {
	return m.db.Model(&MemberStat{}).Where("member_id=?", memberID).UpdateColumn("view", gorm.Expr("view + ?", step)).Error
}

func (m *MemberStatModel) IncreaseLike(memberID int64, step int64) error {
	return m.db.Model(&MemberStat{}).Where("member_id=?", memberID).UpdateColumn("nice_num", gorm.Expr("nice_num + ?", step)).Error
}
