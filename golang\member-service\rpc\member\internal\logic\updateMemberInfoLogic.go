package logic

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"member/internal/svc"
	"member/member"
	"member/model"
)

type UpdateMemberInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateMemberInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateMemberInfoLogic {
	return &UpdateMemberInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UpdateMemberInfoLogic) UpdateMemberInfo(in *member.UpdateMemberInfoReq) (*member.CommonResp, error) {

	// 参数校验
	if in.MemberId <= 0 {
		return &member.CommonResp{
			Code:    400,
			Message: "用户ID不能为空",
		}, nil
	}

	//  先查询该用户是否存在
	memberData, err := l.svcCtx.MemberModel.FindByID(in.MemberId)
	if err != nil {
		return &member.CommonResp{
			Code:    404,
			Message: "查询用户信息失败",
		}, nil
	}

	if memberData == nil {
		return &member.CommonResp{
			Code:    400,
			Message: "该用户不存在",
		}, nil
	}

	// 检查用户状态---是否以删除
	if memberData.Status == -1 {
		return &member.CommonResp{
			Code:    404,
			Message: "用户已被删除",
		}, nil
	}

	var Birthday *string
	if in.Birthday != "" {
		Birthday = &in.Birthday
	}

	memberInfo := &model.Member{
		Realname:     in.Realname,
		Nickname:     in.Nickname,
		HeadPortrait: in.HeadPortrait,
		Gender:       in.Gender,
		QQ:           in.Qq,
		Email:        in.Email,
		Birthday:     Birthday,
		ProvinceID:   in.ProvinceId,
		CityID:       in.CityId,
		AreaID:       in.AreaId,
		Address:      in.Address,
		TelNo:        in.TelNo,
		Description:  in.Description,
	}

	err = l.svcCtx.MemberModel.Update(memberInfo)
	if err != nil {
		return &member.CommonResp{
			Code:    500,
			Message: fmt.Sprintf("更新用户信息失败:%v", err),
		}, nil
	}
	return &member.CommonResp{
		Code:    200,
		Message: "更新用户信息成功",
	}, nil
}
