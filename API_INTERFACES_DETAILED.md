# 📋 **项目接口文档 - 详细版**

本文档详细列出了整个项目中所有的API接口和RPC方法的实现状态，包括完整的请求和响应字段定义。

---

## 📊 **实现状态总览**

| 服务 | 总接口数 | 已实现 | 有框架未实现 | 完全未实现 | 实现率 |
|------|----------|--------|--------------|------------|--------|
| **Member-Service** | **61** | **21** | **6** | **34** | **34.4%** |
| - HTTP API | 27 | 10 | 12 | 5 | 37.0% |
| - Member RPC | 19 | 5 | 0 | 14 | 26.3% |
| - Account RPC | 15 | 6 | 0 | 9 | 40.0% |
| **Dict-Service** | **45** | **35** | **0** | **10** | **77.8%** |
| - Dict API | 5 | 5 | 0 | 0 | 100% |
| - Dict RPC | 5 | 5 | 0 | 0 | 100% |
| - Dict Category API | 5 | 5 | 0 | 0 | 100% |
| - Dict Category RPC | 5 | 5 | 0 | 0 | 100% |
| - Dict Item API | 5 | 5 | 0 | 0 | 100% |
| - Dict Item RPC | 5 | 5 | 0 | 0 | 100% |
| **总计** | **106** | **56** | **6** | **44** | **52.8%** |

### **状态说明：**
- ✅ **已实现**：有完整的业务逻辑实现
- 🔄 **有框架未实现**：有API定义和Handler，但业务逻辑是`// todo`
- ❌ **完全未实现**：连API定义都没有

---

## 🏢 **Member-Service (会员服务)**

### **🌐 HTTP API 接口 (27个)**

#### **1. 认证服务 (无需JWT) - 4个接口**

##### **1.1 用户注册** ✅ **已实现**
- **接口**: `POST /api/v1/auth/register`
- **描述**: 支持用户名/手机号注册

**请求参数 (RegisterReq):**
```json
{
  "username": "string",      // 用户名，必填
  "password": "string",      // 密码，必填
  "mobile": "string",        // 手机号，必填
  "email": "string",         // 邮箱，可选
  "nickname": "string",      // 昵称，可选
  "verify_code": "string",   // 验证码，必填
  "source": "string"         // 来源，可选
}
```

**响应数据 (RegisterResp):**
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "member_id": 12345       // 用户ID
  }
}
```

##### **1.2 用户登录** ✅ **已实现**
- **接口**: `POST /api/v1/auth/login`
- **描述**: 支持用户名/手机号登录

**请求参数 (LoginReq):**
```json
{
  "username": "string",      // 用户名/手机号，必填
  "password": "string",      // 密码，必填
  "login_type": "string",    // 登录类型: username, mobile, email
  "verify_code": "string"    // 验证码，可选
}
```

**响应数据 (LoginResp):**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "string",    // 访问令牌
    "refresh_token": "string",   // 刷新令牌
    "expires_in": 3600,          // 过期时间(秒)
    "user_info": {               // 用户信息
      "id": 12345,
      "username": "string",
      "nickname": "string",
      "mobile": "string",
      "email": "string",
      "head_portrait": "string",
      "gender": 1,
      "status": 1,
      "created_at": **********
    }
  }
}
```

##### **1.3 重置密码** ✅ **已实现**
- **接口**: `POST /api/v1/auth/reset-password`
- **描述**: 密码重置功能

**请求参数 (ResetPasswordReq):**
```json
{
  "mobile": "string",        // 手机号，必填
  "new_password": "string",  // 新密码，必填
  "verify_code": "string"    // 验证码，必填
}
```

**响应数据 (CommonResp):**
```json
{
  "code": 200,
  "message": "密码重置成功"
}
```

##### **1.4 刷新Token** ✅ **已实现**
- **接口**: `POST /api/v1/auth/refresh`
- **描述**: JWT Token刷新

**请求参数 (RefreshTokenReq):**
```json
{
  "refresh_token": "string"  // 刷新令牌，必填
}
```

**响应数据 (RefreshTokenResp):**
```json
{
  "code": 200,
  "message": "刷新成功",
  "data": {
    "access_token": "string",    // 新的访问令牌
    "refresh_token": "string",   // 新的刷新令牌
    "expires_in": 3600           // 过期时间(秒)
  }
}
```

#### **2. 用户服务 (需要JWT) - 11个接口**

##### **2.1 获取用户信息** ✅ **已实现**
- **接口**: `GET /api/v1/member/info`
- **描述**: 获取当前用户详细信息
- **认证**: 需要JWT Token

**请求参数 (GetMemberInfoReq):**
```json
{
  "member_id": 12345  // 用户ID，可选，默认获取当前用户
}
```

**响应数据 (GetMemberInfoResp):**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 12345,
    "merchant_id": 1,
    "store_id": 1,
    "username": "testuser",
    "type": 1,
    "realname": "张三",
    "nickname": "小张",
    "head_portrait": "https://example.com/avatar.jpg",
    "gender": 1,                    // 1:男 2:女 0:未知
    "qq": "123456789",
    "email": "<EMAIL>",
    "birthday": "1990-01-01",
    "province_id": 110000,
    "city_id": 110100,
    "area_id": 110101,
    "address": "北京市朝阳区",
    "mobile": "13800138000",
    "tel_no": "010-12345678",
    "bg_image": "https://example.com/bg.jpg",
    "description": "这是我的个人描述",
    "visit_count": 100,
    "last_time": **********,
    "last_ip": "***********",
    "role": 1,
    "current_level": 1,
    "level_expiration_time": 1672531200,
    "level_buy_type": 1,
    "pid": 0,
    "level": 1,
    "tree": "0,12345",
    "promoter_code": "ABC123",
    "certification_type": 1,
    "source": "web",
    "status": 1,                    // -1:删除 0:禁用 1:正常
    "created_at": **********,
    "updated_at": **********,
    "region_id": 1
  }
}
```

##### **2.2 更新用户信息** ✅ **已实现**
- **接口**: `PUT /api/v1/member/info`
- **描述**: 更新用户基本信息
- **认证**: 需要JWT Token
- **状态**: 完整实现，包含JWT认证和RPC调用

**请求参数 (UpdateMemberInfoReq):**
```json
{
  "realname": "string",         // 真实姓名，可选
  "nickname": "string",         // 昵称，可选
  "head_portrait": "string",    // 头像URL，可选
  "gender": 1,                  // 性别，可选
  "qq": "string",               // QQ号，可选
  "email": "string",            // 邮箱，可选
  "birthday": "1990-01-01",     // 生日，可选
  "province_id": 110000,        // 省份ID，可选
  "city_id": 110100,            // 城市ID，可选
  "area_id": 110101,            // 区域ID，可选
  "address": "string",          // 详细地址，可选
  "tel_no": "string",           // 电话号码，可选
  "bg_image": "string",         // 背景图片，可选
  "description": "string"       // 个人描述，可选
}
```

**响应数据 (CommonResp):**
```json
{
  "code": 200,
  "message": "更新成功"
}
```

##### **2.3 用户登出** ✅ **已实现**
- **接口**: `POST /api/v1/member/logout`
- **描述**: 用户登出处理
- **认证**: 需要JWT Token
- **状态**: 完整实现，调用RPC服务处理登出

**请求参数 (LogoutReq):**
```json
{
  "token": "string"  // Token，可选
}
```

**响应数据 (CommonResp):**
```json
{
  "code": 200,
  "message": "登出成功"
}
```

##### **2.4 获取账户信息** ✅ **已实现**
- **接口**: `GET /api/v1/member/account`
- **描述**: 获取用户账户余额等信息
- **认证**: 需要JWT Token
- **状态**: 完整实现，调用Account RPC服务获取账户信息

**请求参数 (GetAccountInfoReq):**
```json
{
  "member_id": 12345  // 用户ID，可选，默认获取当前用户
}
```

**响应数据 (GetAccountInfoResp):**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "merchant_id": 1,
    "store_id": 1,
    "member_id": 12345,
    "member_type": 1,
    "user_money": 1000.50,           // 当前余额
    "accumulate_money": 5000.00,     // 累计余额
    "give_money": 100.00,            // 累计赠送余额
    "consume_money": 4000.00,        // 累计消费金额
    "frozen_money": 50.00,           // 冻结金额
    "user_integral": 2000,           // 当前积分
    "accumulate_integral": 10000,    // 累计积分
    "give_integral": 500,            // 累计赠送积分
    "consume_integral": 8000.00,     // 累计消费积分
    "frozen_integral": 100,          // 冻结积分
    "user_growth": 1500,             // 当前成长值
    "accumulate_growth": 5000,       // 累计成长值
    "consume_growth": 3500,          // 累计消费成长值
    "frozen_growth": 0,              // 冻结成长值
    "economize_money": 200.00,       // 已节约金额
    "accumulate_drawn_money": 500.00, // 累计提现
    "status": 1,                     // 状态
    "created_at": **********,
    "updated_at": **********
  }
}
```

##### **2.5 充值** ✅ **已实现**
- **接口**: `POST /api/v1/member/account/recharge`
- **描述**: 用户账户充值
- **认证**: 需要JWT Token
- **状态**: 完整实现，包含JWT认证、参数验证和Account RPC调用

**请求参数 (RechargeReq):**
```json
{
  "amount": 100.00,           // 充值金额，必填
  "payment_type": "alipay",   // 支付方式: alipay, wechat, bank
  "remark": "充值备注"        // 备注，可选
}
```

**响应数据 (RechargeResp):**
```json
{
  "code": 200,
  "message": "充值订单创建成功",
  "data": {
    "order_no": "R20240101123456",  // 订单号
    "amount": 100.00,               // 充值金额
    "pay_url": "https://...",       // 支付链接，可选
    "qr_code": "data:image/png..."  // 二维码，可选
  }
}
```

##### **2.6 提现** ✅ **已实现**
- **接口**: `POST /api/v1/member/account/withdraw`
- **描述**: 用户账户提现
- **认证**: 需要JWT Token
- **状态**: 完整实现，包含JWT认证、参数验证和Account RPC调用

**请求参数 (WithdrawReq):**
```json
{
  "amount": 100.00,           // 提现金额，必填
  "account_type": "bank",     // 账户类型: bank, alipay, wechat
  "account_no": "6222...",    // 账户号码，必填
  "account_name": "张三",     // 账户姓名，必填
  "bank_name": "中国银行",    // 银行名称，可选
  "remark": "提现备注"        // 备注，可选
}
```

**响应数据 (WithdrawResp):**
```json
{
  "code": 200,
  "message": "提现申请成功",
  "data": {
    "withdraw_no": "W20240101123456", // 提现单号
    "amount": 100.00,                 // 提现金额
    "fee": 2.00,                      // 手续费
    "actual_amount": 98.00            // 实际到账金额
  }
}
```

##### **2.7 获取实名认证信息** 🔄 **有框架未实现**
- **接口**: `GET /api/v1/member/certification`
- **描述**: 获取当前用户的实名认证信息
- **认证**: 需要JWT Token
- **状态**: Handler和类型定义已生成，但业务逻辑是`// todo`

**请求参数 (GetCertificationInfoReq):**
```json
{
  "member_id": 12345  // 用户ID，可选，默认获取当前用户
}
```

**响应数据 (GetCertificationInfoResp):**
```json
{
  "code": 200,
  "message": "获取认证信息成功",
  "data": {
    "id": 1,
    "merchant_id": 1,
    "store_id": 1,
    "member_id": 12345,
    "member_type": 1,
    "realname": "张三",
    "identity_card": "110101199001011234",
    "identity_card_front": "https://example.com/front.jpg",  // 身份证正面照片
    "identity_card_back": "https://example.com/back.jpg",    // 身份证背面照片
    "gender": "男",
    "birthday": "1990-01-01",
    "front_is_fake": 0,                    // 正面是否伪造 0:否 1:是
    "back_is_fake": 0,                     // 背面是否伪造 0:否 1:是
    "nationality": "汉族",
    "address": "北京市朝阳区某某街道",
    "start_date": "2010-01-01",            // 证件有效期开始
    "end_date": "2030-01-01",              // 证件有效期结束
    "issue": "北京市公安局朝阳分局",
    "is_self": 1,                          // 是否本人 0:否 1:是
    "status": 1,                           // 认证状态 0:待审核 1:已通过 -1:已拒绝
    "created_at": **********,
    "updated_at": **********
  }
}
```

##### **2.8 提交实名认证** 🔄 **有框架未实现**
- **接口**: `POST /api/v1/member/certification`
- **描述**: 提交用户实名认证信息
- **认证**: 需要JWT Token
- **状态**: Handler和类型定义已生成，但业务逻辑是`// todo`

**请求参数 (SubmitCertificationReq):**
```json
{
  "realname": "张三",                                    // 真实姓名，必填
  "identity_card": "110101199001011234",                // 身份证号，必填
  "identity_card_front": "https://example.com/front.jpg", // 身份证正面照片URL，必填
  "identity_card_back": "https://example.com/back.jpg",   // 身份证背面照片URL，必填
  "gender": "男",                                        // 性别，可选
  "birthday": "1990-01-01",                             // 出生日期，可选
  "nationality": "汉族",                                 // 民族，可选
  "address": "北京市朝阳区某某街道",                      // 地址，可选
  "start_date": "2010-01-01",                           // 证件有效期开始，可选
  "end_date": "2030-01-01",                             // 证件有效期结束，可选
  "issue": "北京市公安局朝阳分局",                        // 签发机关，可选
  "is_self": 1                                          // 是否本人认证，可选，默认1
}
```

**响应数据 (CommonResp):**
```json
{
  "code": 200,
  "message": "实名认证信息提交成功，请等待审核"
}
```

##### **2.9 获取统计信息** 🔄 **有框架未实现**
- **接口**: `GET /api/v1/member/stat`
- **描述**: 获取当前用户的统计信息
- **认证**: 需要JWT Token
- **状态**: Handler和类型定义已生成，但业务逻辑是`// todo`

**请求参数 (GetMemberStatReq):**
```json
{
  "member_id": 12345  // 用户ID，可选，默认获取当前用户
}
```

**响应数据 (GetMemberStatResp):**
```json
{
  "code": 200,
  "message": "获取统计信息成功",
  "data": {
    "id": 1,
    "merchant_id": 1,
    "store_id": 1,
    "member_id": 12345,
    "member_type": 1,
    "nice_num": 120,                      // 点赞数量
    "disagree_num": 5,                    // 不赞同数量
    "transmit_num": 30,                   // 转发数量
    "comment_num": 45,                    // 评论数量
    "collect_num": 15,                    // 收藏数量
    "report_num": 0,                      // 举报数量
    "recommend_num": 8,                   // 推荐数量
    "follow_num": 25,                     // 关注人数
    "allowed_num": 50,                    // 被关注人数
    "view": 1500,                         // 浏览量
    "status": 1,
    "created_at": **********,
    "updated_at": **********
  }
}
```

##### **2.10 提交注销申请** 🔄 **有框架未实现**
- **接口**: `POST /api/v1/member/cancel`
- **描述**: 提交用户账户注销申请
- **认证**: 需要JWT Token
- **状态**: Handler和类型定义已生成，但业务逻辑是`// todo`

**请求参数 (SubmitCancelReq):**
```json
{
  "content": "由于个人原因，申请注销账户。已确认账户内无重要数据和资金。" // 申请原因，必填
}
```

**响应数据 (CommonResp):**
```json
{
  "code": 200,
  "message": "注销申请提交成功，请等待审核"
}
```

##### **2.11 获取注销状态** 🔄 **有框架未实现**
- **接口**: `GET /api/v1/member/cancel/status`
- **描述**: 获取当前用户账户注销申请的状态
- **认证**: 需要JWT Token
- **状态**: Handler和类型定义已生成，但业务逻辑是`// todo`

**请求参数 (GetCancelStatusReq):**
```json
{
  "member_id": 12345  // 用户ID，可选，默认获取当前用户
}
```

**响应数据 (GetCancelStatusResp):**
```json
{
  "code": 200,
  "message": "获取注销状态成功",
  "data": {
    "has_application": true,              // 是否有申请记录
    "audit_status": 0,                    // 审核状态 0:申请中 1:已通过 -1:已拒绝
    "status_text": "审核中",             // 状态描述
    "content": "由于个人原因，申请注销账户。已确认账户内无重要数据和资金。", // 申请内容
    "submit_time": **********,            // 提交时间
    "audit_time": 0,                      // 审核时间（0表示未审核）
    "refusal_cause": ""                   // 拒绝原因（审核拒绝时填写）
  }
}
```

### **🔧 Account RPC 接口 (15个)**

#### **1. 账户信息管理 (2/3 已实现)**

##### **1.1 创建账户** ✅ **已实现**
- **方法**: `CreateAccount(CreateAccountReq) returns (CreateAccountResp)`
- **描述**: 为新会员创建默认账户
- **实现状态**: 完整实现，包含参数验证、重复检查、默认账户创建

##### **1.2 获取账户信息** ✅ **已实现**
- **方法**: `GetAccountInfo(GetAccountInfoReq) returns (GetAccountInfoResp)`
- **描述**: 获取会员账户详细信息
- **实现状态**: 完整实现，返回完整的账户信息

##### **1.3 获取账户列表** ❌ **完全未实现**
- **方法**: `GetAccountList(GetAccountListReq) returns (GetAccountListResp)`
- **描述**: 分页获取账户列表
- **实现状态**: 仅有框架代码，业务逻辑待实现

#### **2. 余额操作 (4/5 已实现)**

##### **2.1 获取余额** ✅ **已实现**
- **方法**: `GetBalance(GetBalanceReq) returns (GetBalanceResp)`
- **描述**: 获取会员账户余额信息
- **实现状态**: 完整实现，包含可用余额、冻结余额、累计消费等信息

##### **2.2 充值** ✅ **已实现**
- **方法**: `Recharge(RechargeReq) returns (RechargeResp)`
- **描述**: 会员账户充值功能
- **实现状态**: 完整实现，包含支付方式验证、订单号生成、余额更新

##### **2.3 提现** ✅ **已实现**
- **方法**: `Withdraw(WithdrawReq) returns (WithdrawResp)`
- **描述**: 会员账户提现功能
- **实现状态**: 完整实现，包含余额检查、提现单号生成、审核流程

##### **2.4 冻结资金** ✅ **已实现**
- **方法**: `FreezeMoney(FreezeMoneyReq) returns (CommonResp)`
- **描述**: 冻结会员账户资金
- **实现状态**: 完整实现，包含余额检查、冻结操作、操作日志

##### **2.5 解冻资金** ❌ **完全未实现**
- **方法**: `UnfreezeMoney(UnfreezeMoneyReq) returns (CommonResp)`
- **描述**: 解冻会员账户资金
- **实现状态**: 仅有框架代码，业务逻辑待实现

#### **3. 积分操作 (0/3 已实现)**

##### **3.1 获取积分信息** ❌ **完全未实现**
- **方法**: `GetIntegralInfo(GetIntegralInfoReq) returns (GetIntegralInfoResp)`
- **描述**: 获取会员积分信息
- **实现状态**: 仅有框架代码，业务逻辑待实现

##### **3.2 增加积分** ❌ **完全未实现**
- **方法**: `AddIntegral(AddIntegralReq) returns (CommonResp)`
- **描述**: 给会员增加积分
- **实现状态**: 仅有框架代码，业务逻辑待实现

##### **3.3 消费积分** ❌ **完全未实现**
- **方法**: `ConsumeIntegral(ConsumeIntegralReq) returns (CommonResp)`
- **描述**: 消费会员积分
- **实现状态**: 仅有框架代码，业务逻辑待实现

#### **4. 成长值操作 (0/2 已实现)**

##### **4.1 获取成长值信息** ❌ **完全未实现**
- **方法**: `GetGrowthInfo(GetGrowthInfoReq) returns (GetGrowthInfoResp)`
- **描述**: 获取会员成长值信息
- **实现状态**: 仅有框架代码，业务逻辑待实现

##### **4.2 增加成长值** ❌ **完全未实现**
- **方法**: `AddGrowth(AddGrowthReq) returns (CommonResp)`
- **描述**: 给会员增加成长值
- **实现状态**: 仅有框架代码，业务逻辑待实现

#### **5. 账户日志 (0/3 已实现)**

##### **5.1 添加资金日志** ❌ **完全未实现**
- **方法**: `AddMoneyLog(AddMoneyLogReq) returns (CommonResp)`
- **描述**: 记录资金变动日志
- **实现状态**: 仅有框架代码，业务逻辑待实现

##### **5.2 添加积分日志** ❌ **完全未实现**
- **方法**: `AddIntegralLog(AddIntegralLogReq) returns (CommonResp)`
- **描述**: 记录积分变动日志
- **实现状态**: 仅有框架代码，业务逻辑待实现

##### **5.3 获取账户日志** ❌ **完全未实现**
- **方法**: `GetAccountLog(GetAccountLogReq) returns (GetAccountLogResp)`
- **描述**: 分页获取账户操作日志
- **实现状态**: 仅有框架代码，业务逻辑待实现

### **📊 Account RPC 实现进度**

| 功能模块 | 已实现 | 总数 | 完成率 |
|----------|--------|------|--------|
| **账户信息管理** | 2/3 | 3 | 66.7% |
| **余额操作** | 4/5 | 5 | 80.0% |
| **积分操作** | 0/3 | 3 | 0% |
| **成长值操作** | 0/2 | 2 | 0% |
| **账户日志** | 0/3 | 3 | 0% |
| **总计** | **6/15** | **15** | **40.0%** |

### **🎯 已实现功能亮点**

#### **✅ 核心账户功能已完成：**
- ✅ **账户创建和查询** - 支持新会员账户初始化
- ✅ **完整的充值流程** - 支持多种支付方式验证
- ✅ **安全的提现功能** - 包含审核机制和余额验证
- ✅ **资金冻结管控** - 风控和安全功能
- ✅ **余额查询服务** - 详细的账户余额信息

#### **🔧 代码质量特点：**
- 🔧 **严格参数验证** - 所有接口都有完整的参数校验
- 🔧 **统一错误处理** - 规范的错误码和消息格式
- 🔧 **详细操作日志** - 关键操作都有审计日志
- 🔧 **数据库事务** - 确保数据一致性和原子性
- 🔧 **GORM模型集成** - 完整的ORM模型和钩子函数

#### **💼 业务逻辑完善：**
- 💼 **订单号生成** - 规范的交易单号格式
- 💼 **余额验证** - 充分的资金安全检查
- 💼 **状态管理** - 完善的账户状态控制
- 💼 **用户体验** - 友好的提示信息和响应

### **⚡ 建议优先实现的剩余功能：**

#### **高优先级（必须实现）：**
1. **UnfreezeMoney** - 配套FreezeMoney使用
2. **GetAccountList** - 管理后台必需功能

#### **中优先级（建议实现）：**
1. **AddIntegral/ConsumeIntegral** - 积分系统核心
2. **GetIntegralInfo** - 积分查询功能

#### **低优先级（可选实现）：**
1. **AddGrowth/GetGrowthInfo** - 成长值系统
2. **各种日志功能** - 操作审计

#### **3. 管理员服务 (需要JWT+AdminAuth) - 12个接口** ❌ **完全未实现**

所有管理员接口都只有API定义，没有生成Handler和Logic文件。

---

## 📚 **Dict-Service (字典服务)**

### **🌐 HTTP API 接口 (15个)**

#### **1. 字典管理 API - 5个接口** ✅ **已实现**

##### **1.1 创建字典** ✅ **已实现**
- **接口**: `POST /api/dict/create`
- **描述**: 创建新的字典

**请求参数 (CreateDictReq):**
```json
{
  "tenantId": 1,          // 租户ID，可选
  "categoryId": 1,        // 分类ID，可选
  "code": "USER_DICT",    // 字典编码，必填
  "name": "用户字典",     // 字典名称，必填
  "showType": "select",   // 显示类型，必填
  "remark": "用户相关字典", // 说明，可选
  "status": 1,            // 状态，可选，默认1
  "createdBy": 12345      // 创建人ID，可选
}
```

**响应数据 (CreateDictResp):**
```json
{
  "code": 200,
  "msg": "创建成功",
  "base": {
    "code": 200,
    "msg": "创建字典成功",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "id": 1001              // 新创建的字典ID
}
```

##### **1.2 更新字典** ✅ **已实现**
- **接口**: `PUT /api/v1/dict/update/:id`
- **描述**: 更新现有字典信息

**请求参数 (UpdateDictReq):**
```json
{
  "id": 1001,             // 字典ID，路径参数，必填
  "code": "USER_DICT_V2", // 字典编码，可选
  "name": "用户字典v2",   // 字典名称，可选
  "remark": "用户相关字典更新版", // 说明，可选
  "status": 1             // 状态：1=启用，0=禁用，可选
}
```

**响应数据 (UpdateDictResp):**
```json
{
  "success": true,
  "message": "更新字典成功"
}
```

##### **1.3 删除字典** ✅ **已实现**
- **接口**: `DELETE /api/v1/dict/delete/:id`
- **描述**: 删除指定字典

**请求参数 (DeleteDictReq):**
```json
{
  "id": 1001             // 字典ID，路径参数，必填
}
```

**响应数据 (DeleteDictResp):**
```json
{
  "success": true,
  "message": "删除字典成功"
}
```

##### **1.4 获取字典详情** ✅ **已实现**
- **接口**: `GET /api/v1/dict/detail/:id`
- **描述**: 获取指定字典的详细信息

**请求参数 (GetDictReq):**
```json
{
  "id": 1001             // 字典ID，路径参数，必填
}
```

**响应数据 (GetDictResp):**
```json
{
  "message": "获取字典详情成功",
  "dict": {
    "id": 1001,
    "code": "USER_DICT",
    "name": "用户字典",
    "remark": "用户相关字典",
    "status": 1,
    "created_time": "2024-01-01 12:00:00",
    "updated_time": "2024-01-01 13:00:00"
  }
}
```

##### **1.5 字典列表** ✅ **已实现**
- **接口**: `GET /api/v1/dict/list`
- **描述**: 分页获取字典列表

**请求参数 (ListDictReq):**
```json
{
  "page": 1,              // 页码，可选，默认1
  "page_size": 20,        // 每页数量，可选，默认10
  "code": "USER",         // 字典编码筛选（模糊查询），可选
  "name": "用户",         // 字典名称筛选（模糊查询），可选
  "status": 1             // 状态筛选：-1=全部，0=禁用，1=启用，默认-1
}
```

**响应数据 (ListDictResp):**
```json
{
  "message": "获取字典列表成功",
  "total": 50,
  "list": [
    {
      "id": 1001,
      "code": "USER_DICT",
      "name": "用户字典",
      "remark": "用户相关字典",
      "status": 1,
      "created_time": "2024-01-01 12:00:00",
      "updated_time": "2024-01-01 13:00:00"
    },
    {
      "id": 1002,
      "code": "ORDER_DICT",
      "name": "订单字典",
      "remark": "订单相关字典",
      "status": 1,
      "created_time": "2024-01-01 14:00:00",
      "updated_time": "2024-01-01 15:00:00"
    }
  ]
}
```

#### **2. 字典分类管理 API - 5个接口** ✅ **完全实现**

##### **2.1 创建字典分类** ✅ **已实现**
- **接口**: `POST /api/dict/category/create`
- **描述**: 创建新的字典分类

**请求参数 (CreateDictCategoryReq):**
```json
{
  "dictId": 1,            // 字典ID，必填
  "tenantId": 1,          // 租户ID，可选
  "name": "用户分类",     // 分类名称，必填
  "sort": 1.0,            // 排序，可选，默认0
  "status": 1,            // 状态，可选，默认1
  "createdBy": 12345      // 创建人ID，可选
}
```

**响应数据 (CreateDictCategoryResp):**
```json
{
  "code": 200,
  "msg": "创建成功",
  "base": {
    "code": 200,
    "msg": "调用RPC的CreateDictCategory创建成功"
  },
  "id": 1001              // 新创建的分类ID
}
```

##### **2.2 获取字典分类详情** ✅ **已实现**
- **接口**: `POST /api/dict/category/get`
- **描述**: 获取字典分类详细信息

**请求参数 (GetDictCategoryReq):**
```json
{
  "id": 1001              // 分类ID，必填
}
```

**响应数据 (GetDictCategoryResp):**
```json
{
  "code": 200,
  "msg": "获取成功",
  "base": {
    "code": 200,
    "msg": "调用RPC的GetDictCategory获取数据成功"
  },
  "dictCategory": {
    "id": 1001,
    "tenantId": 1,
    "name": "用户分类",
    "sort": 1.0,
    "status": 1,
    "createdBy": 12345,
    "updatedBy": 12345,
    "createdTime": "2024-01-01 12:00:00",
    "updatedTime": "2024-01-01 13:00:00"
  }
}
```

##### **2.3 更新字典分类** ✅ **已实现**
- **接口**: `POST /api/dict/category/update`
- **描述**: 更新字典分类信息

**请求参数 (UpdateDictCategoryReq):**
```json
{
  "id": 1001,             // 分类ID，必填
  "tenantId": 1,          // 租户ID，可选
  "name": "VIP用户分类",  // 分类名称，可选
  "sort": 2.0,            // 排序，可选
  "status": 1,            // 状态，可选
  "updatedBy": 12345      // 更新人ID，必填
}
```

**响应数据 (UpdateDictCategoryResp):**
```json
{
  "code": 200,
  "msg": "更新成功",
  "base": {
    "code": 200,
    "msg": "调用RPC的UpdateDictCategory更新成功"
  },
  "success": true
}
```

##### **2.4 删除字典分类** ✅ **已实现**
- **接口**: `POST /api/dict/category/delete`
- **描述**: 删除字典分类

**请求参数 (DeleteDictCategoryReq):**
```json
{
  "id": 1001,             // 分类ID，必填
  "deletedBy": 12345      // 删除人ID，必填
}
```

**响应数据 (DeleteDictCategoryResp):**
```json
{
  "code": 200,
  "msg": "删除成功",
  "base": {
    "code": 200,
    "msg": "调用RPC的DeleteDictCategory字典分类删除成功"
  },
  "success": true
}
```

##### **2.5 字典分类列表** ✅ **已实现**
- **接口**: `POST /api/dict/category/list`
- **描述**: 分页获取字典分类列表

**请求参数 (ListDictCategoryReq):**
```json
{
  "page": 1,              // 页码，可选，默认1
  "pageSize": 20,         // 每页数量，可选，默认20
  "name": "用户",         // 名称筛选，可选
  "status": 1             // 状态筛选，可选
}
```

**响应数据 (ListDictCategoryResp):**
```json
{
  "code": 200,
  "msg": "获取字典分类列表成功",
  "base": {
    "code": 200,
    "msg": "获取字典分类列表成功"
  },
  "total": 50,
  "list": [
    {
      "id": 1001,
      "tenantId": 1,
      "name": "用户分类",
      "sort": 1.0,
      "status": 1,
      "createdBy": 12345,
      "updatedBy": 12345,
      "createdTime": "2024-01-01 12:00:00",
      "updatedTime": "2024-01-01 13:00:00"
    }
  ]
}
```

#### **3. 字典项管理 API - 5个接口** ✅ **已实现**

所有字典项接口都有完整的业务逻辑实现：

##### **3.1 创建字典项** ✅ **已实现**
- **接口**: `POST /api/dictitem/create`
- **描述**: 创建新的字典项

**请求参数 (CreateDictItemReq):**
```json
{
  "tenantId": 1,          // 租户ID，可选
  "dictId": 1,            // 字典ID，必填
  "categoryId": 1,        // 分类ID，必填
  "code": "USER_TYPE_1",  // 编码，必填
  "name": "普通用户",     // 名称，必填
  "pid": 0,               // 父级ID，可选，默认0
  "sort": 1.0,            // 排序，可选，默认99
  "status": 1,            // 状态，可选，默认1
  "createdBy": 12345      // 创建人ID，必填
}
```

**响应数据 (CreateDictItemResp):**
```json
{
  "code": 200,
  "msg": "创建成功",
  "id": 1001              // 新创建的字典项ID
}
```

##### **3.2 更新字典项** ✅ **已实现**
- **接口**: `PUT /api/v1/dictitem/update/:id`
- **描述**: 更新现有字典项信息

**请求参数 (UpdateDictItemReq):**
```json
{
  "id": 1001,             // 字典项ID，路径参数，必填
  "dict_id": 1,           // 字典ID，可选
  "category_id": 1,       // 分类ID，可选
  "code": "USER_TYPE_VIP", // 编码，可选
  "name": "VIP用户",      // 名称，可选
  "status": 1             // 状态：1=启用，0=禁用，可选
}
```

**响应数据 (UpdateDictItemResp):**
```json
{
  "success": true,
  "message": "更新字典项成功"
}
```

##### **3.3 删除字典项** ✅ **已实现**
- **接口**: `DELETE /api/v1/dictitem/delete/:id`
- **描述**: 删除指定字典项

**请求参数 (DeleteDictItemReq):**
```json
{
  "id": 1001             // 字典项ID，路径参数，必填
}
```

**响应数据 (DeleteDictItemResp):**
```json
{
  "success": true,
  "message": "删除字典项成功"
}
```

##### **3.4 获取字典项详情** ✅ **已实现**
- **接口**: `GET /api/v1/dictitem/detail/:id`
- **描述**: 获取指定字典项的详细信息

**请求参数 (GetDictItemReq):**
```json
{
  "id": 1001             // 字典项ID，路径参数，必填
}
```

**响应数据 (GetDictItemResp):**
```json
{
  "message": "获取字典项详情成功",
  "item": {
    "id": 1001,
    "dict_id": 1,
    "category_id": 1,
    "code": "USER_TYPE_1",
    "name": "普通用户",
    "status": 1,
    "created_time": "2024-01-01 12:00:00",
    "updated_time": "2024-01-01 13:00:00"
  }
}
```

##### **3.5 字典项列表** ✅ **已实现**
- **接口**: `GET /api/v1/dictitem/list`
- **描述**: 分页获取字典项列表

**请求参数 (ListDictItemReq):**
```json
{
  "page": 1,              // 页码，可选，默认1
  "page_size": 20,        // 每页数量，可选，默认10
  "dict_id": 1,           // 字典ID筛选，可选
  "category_id": 1,       // 分类ID筛选，可选
  "code": "USER_TYPE",    // 编码筛选（模糊查询），可选
  "name": "用户",         // 名称筛选（模糊查询），可选
  "status": 1             // 状态筛选：-1=全部，0=禁用，1=启用，默认-1
}
```

**响应数据 (ListDictItemResp):**
```json
{
  "message": "获取字典项列表成功",
  "total": 25,
  "list": [
    {
      "id": 1001,
      "dict_id": 1,
      "category_id": 1,
      "code": "USER_TYPE_1",
      "name": "普通用户",
      "status": 1,
      "created_time": "2024-01-01 12:00:00",
      "updated_time": "2024-01-01 13:00:00"
    },
    {
      "id": 1002,
      "dict_id": 1,
      "category_id": 1,
      "code": "USER_TYPE_2",
      "name": "VIP用户",
      "status": 1,
      "created_time": "2024-01-01 14:00:00",
      "updated_time": "2024-01-01 15:00:00"
    }
  ]
}
```

---

## 🔧 **通用数据结构**

### **基础响应结构**
```json
{
  "code": 200,            // 状态码: 200=成功, 400=参数错误, 401=未授权, 500=服务器错误
  "message": "操作成功",  // 响应消息
  "data": {}              // 响应数据，可选
}
```

### **分页信息结构**
```json
{
  "page": 1,              // 当前页码
  "page_size": 20,        // 每页数量
  "total": 100,           // 总记录数
  "total_pages": 5        // 总页数
}
```

### **状态码说明**
- `1`: 正常/启用
- `0`: 禁用
- `-1`: 删除

### **性别枚举**
- `0`: 未知
- `1`: 男
- `2`: 女

### **用户类型枚举**
- `1`: 普通用户
- `2`: VIP用户
- `9`: 管理员

---

## 📋 **接口测试清单**

### **✅ 已完全实现的接口**

#### **Member-Service HTTP API (10个)**
- [x] `POST /api/v1/auth/register` - 用户注册
- [x] `POST /api/v1/auth/login` - 用户登录  
- [x] `POST /api/v1/auth/reset-password` - 重置密码
- [x] `POST /api/v1/auth/refresh` - 刷新Token
- [x] `GET /api/v1/member/info` - 获取用户信息
- [x] `PUT /api/v1/member/info` - 更新用户信息
- [x] `POST /api/v1/member/logout` - 用户登出
- [x] `GET /api/v1/member/account` - 获取账户信息
- [x] `POST /api/v1/member/account/recharge` - 充值
- [x] `POST /api/v1/member/account/withdraw` - 提现

#### **Account RPC Service (6个)**
- [x] `CreateAccount` - 创建会员账户
- [x] `GetAccountInfo` - 获取账户信息
- [x] `GetBalance` - 获取余额信息
- [x] `Recharge` - 账户充值
- [x] `Withdraw` - 账户提现
- [x] `FreezeMoney` - 冻结资金

#### **Dict-Service (15个)**
- [x] `POST /api/dict/create` - 创建字典
- [x] `POST /api/dict/update` - 更新字典
- [x] `POST /api/dict/delete` - 删除字典
- [x] `POST /api/dict/get` - 获取字典详情
- [x] `POST /api/dict/list` - 字典列表
- [x] `POST /api/dict/category/create` - 创建字典分类
- [x] `POST /api/dict/category/get` - 获取字典分类详情
- [x] `POST /api/dict/category/update` - 更新字典分类
- [x] `POST /api/dict/category/delete` - 删除字典分类
- [x] `POST /api/dict/category/list` - 字典分类列表
- [x] `POST /api/dictitem/create` - 创建字典项
- [x] `POST /api/dictitem/update` - 更新字典项
- [x] `POST /api/dictitem/delete` - 删除字典项
- [x] `POST /api/dictitem/get` - 获取字典项详情
- [x] `POST /api/dictitem/list` - 字典项列表

### **🔄 有框架但需要实现业务逻辑的接口 (21个)**

#### **Member-Service HTTP API (12个)**
- [ ] `GET /api/v1/member/certification` - 获取实名认证信息
- [ ] `POST /api/v1/member/certification` - 提交实名认证
- [ ] `GET /api/v1/member/stat` - 获取统计信息
- [ ] `POST /api/v1/member/cancel` - 提交注销申请
- [ ] `GET /api/v1/member/cancel/status` - 获取注销状态
- [ ] `GET /api/v1/admin/member/list` - 获取用户列表
- [ ] `GET /api/v1/admin/member/detail` - 获取用户详情
- [ ] `DELETE /api/v1/admin/member/delete` - 删除用户
- [ ] `POST /api/v1/admin/member/freeze` - 冻结资金
- [ ] `POST /api/v1/admin/member/unfreeze` - 解冻资金
- [ ] `GET /api/v1/admin/certification/pending` - 获取待审核认证
- [ ] `POST /api/v1/admin/certification/audit` - 审核实名认证

#### **Account RPC Service (9个)**
- [ ] `GetAccountList` - 获取账户列表
- [ ] `UnfreezeMoney` - 解冻资金
- [ ] `GetIntegralInfo` - 获取积分信息
- [ ] `AddIntegral` - 增加积分
- [ ] `ConsumeIntegral` - 消费积分
- [ ] `GetGrowthInfo` - 获取成长值信息
- [ ] `AddGrowth` - 增加成长值
- [ ] `AddMoneyLog` - 添加资金日志
- [ ] `AddIntegralLog` - 添加积分日志
- [ ] `GetAccountLog` - 获取账户日志

#### **Dict-Service (0个)**
- [ ] 所有Dict-Service的接口都已实现完成！

---

## 🚀 **开发优先级建议**

### **🔥 第一优先级 - 完善核心业务**
1. **Account RPC核心功能** (2个) - 最高优先级
   - UnfreezeMoney、GetAccountList
2. **Member用户认证相关** (5个) - 核心用户功能
   - 实名认证信息、提交认证、统计信息、注销申请等
3. **✅ 核心账户功能已完成** - 充值、提现、冻结、余额查询等 ✅

### **🚀 第二优先级 - 扩展功能**
4. **Account RPC积分系统** (5个) - 积分和成长值功能
5. **Account RPC日志系统** (2个) - 操作审计功能
6. **Member RPC剩余接口** (14个) - 用户管理的底层服务

### **⭐ 第三优先级 - 管理功能**
7. **Member管理员接口** (7个) - 后台管理功能
8. **✅ Dict-Service 已完全实现** - 所有接口都已实现完成！

---

## 💡 **实现建议**

1. **优先实现有框架的接口** - 框架代码已生成，只需要补充业务逻辑
2. **先实现核心业务流程** - 账户管理是用户系统的核心
3. **完善错误处理和参数校验** - 确保接口的健壮性
4. **添加单元测试** - 保证代码质量
5. **完善API文档** - 使用Swagger等工具

---

*本文档会随着开发进度持续更新，请关注最新版本。* 