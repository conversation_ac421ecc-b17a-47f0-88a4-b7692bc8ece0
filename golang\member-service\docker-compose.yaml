version: '3.8'

services:
  # ===== 基础设施服务 =====
  mysql:
    image: mysql:8.0
    container_name: member-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: base_db
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - member-network

  redis:
    image: redis:7-alpine
    container_name: member-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - member-network

  etcd:
    image: quay.io/coreos/etcd:v3.5.9
    container_name: member-etcd
    restart: always
    ports:
      - "2379:2379"
      - "2380:2380"
    environment:
      ETCD_NAME: etcd0
      ETCD_DATA_DIR: /etcd-data
      ETCD_LISTEN_CLIENT_URLS: http://0.0.0.0:2379
      ETCD_ADVERTISE_CLIENT_URLS: http://etcd:2379
      ETCD_LISTEN_PEER_URLS: http://0.0.0.0:2380
      ETCD_INITIAL_ADVERTISE_PEER_URLS: http://etcd:2380
      ETCD_INITIAL_CLUSTER: etcd0=http://etcd:2380
      ETCD_INITIAL_CLUSTER_TOKEN: etcd-cluster-token
      ETCD_INITIAL_CLUSTER_STATE: new
    volumes:
      - etcd_data:/etcd-data
    networks:
      - member-network

  # ===== 应用服务 =====
  member-rpc:
    build:
      context: ./rpc/member
      dockerfile: Dockerfile
    container_name: member-rpc
    restart: always
    ports:
      - "8080:8080"
    environment:
      - TZ=Asia/Shanghai
    depends_on:
      - mysql
      - redis
      - etcd
    volumes:
      - ./rpc/member/etc:/root/etc
    networks:
      - member-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  account-rpc:
    build:
      context: ./rpc/account
      dockerfile: Dockerfile
    container_name: account-rpc
    restart: always
    ports:
      - "8081:8081"
    environment:
      - TZ=Asia/Shanghai
    depends_on:
      - mysql
      - redis
      - etcd
    volumes:
      - ./rpc/account/etc:/root/etc
    networks:
      - member-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  member-api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: member-api
    restart: always
    ports:
      - "8888:8888"
    environment:
      - TZ=Asia/Shanghai
    depends_on:
      - member-rpc
      - account-rpc
    volumes:
      - ./api/etc:/root/etc
    networks:
      - member-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8888/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# ===== 网络配置 =====
networks:
  member-network:
    driver: bridge

# ===== 数据卷 =====
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  etcd_data:
    driver: local 