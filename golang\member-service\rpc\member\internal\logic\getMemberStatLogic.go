package logic

import (
	"context"

	"member/internal/svc"
	"member/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMemberStatLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetMemberStatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberStatLogic {
	return &GetMemberStatLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 用户统计
func (l *GetMemberStatLogic) GetMemberStat(in *member.GetMemberStatReq) (*member.GetMemberStatResp, error) {
	// todo: add your logic here and delete this line

	return &member.GetMemberStatResp{}, nil
}
