package member

import (
	"account/account"
	"context"
	"fmt"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type RechargeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 充值
func NewRechargeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RechargeLogic {
	return &RechargeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RechargeLogic) Recharge(req *types.RechargeReq) (resp *types.RechargeResp, err error) {
	var memberId int64

	// 从JWT token 中获取当前用户member_id
	memberIdvalue := l.ctx.Value("member_id")
	if memberIdvalue == nil {
		return nil, fmt.Errorf("无法获取用户身份信息")
	}
	// 开始处理不同类型的member_id的值
	switch v := memberIdvalue.(type) {
	case int64:
		memberId = v
	case float64:
		memberId = int64(v)
	case string:
		if parsed, parseErr := strconv.ParseInt(v, 10, 64); parseErr == nil {
			memberId = parsed
		} else {
			return nil, fmt.Errorf("用户身份信息格式错误")
		}
	default:
		return nil, fmt.Errorf("用户身份信息类型错误: %T", v)
	}

	// 参数校验
	if req.Amount <= 0 {
		return nil, fmt.Errorf("请填写充值金额")
	}
	if req.PaymentType == "" {
		return nil, fmt.Errorf("请选择支付方式")
	}
	if memberId <= 0 {
		return nil, fmt.Errorf("用户ID无效")
	}

	//  调用RPC服务开始处理业务
	rpcResp, err := l.svcCtx.AccountRpc.Recharge(l.ctx, &account.RechargeReq{
		MemberId:      memberId,
		Amount:        req.Amount,
		PaymentMethod: req.PaymentType,
		Remark:        req.Remark,
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC充值失败: %v", err)
	}

	return &types.RechargeResp{
		Code:    int(rpcResp.Code),
		Message: rpcResp.Message,
		Data: types.RechargeData{
			OrderNo: rpcResp.OrderNo,
			Amount:  rpcResp.BalanceAfter,
			PayUrl: req.PaymentType,
		},
	}, nil
}
