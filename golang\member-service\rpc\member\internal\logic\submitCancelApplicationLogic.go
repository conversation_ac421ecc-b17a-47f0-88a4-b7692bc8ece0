package logic

import (
	"context"

	"member/internal/svc"
	"member/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type SubmitCancelApplicationLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSubmitCancelApplicationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubmitCancelApplicationLogic {
	return &SubmitCancelApplicationLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 注销申请
func (l *SubmitCancelApplicationLogic) SubmitCancelApplication(in *member.SubmitCancelApplicationReq) (*member.CommonResp, error) {
	// todo: add your logic here and delete this line

	return &member.CommonResp{}, nil
}
