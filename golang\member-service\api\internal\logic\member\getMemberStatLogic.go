package member

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type GetMemberStatLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取统计信息
func NewGetMemberStatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberStatLogic {
	return &GetMemberStatLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMemberStatLogic) GetMemberStat(req *types.GetMemberStatReq) (resp *types.GetMemberStatResp, err error) {
	var memberId int64
	// 从JWT token 中获取当前用户ID
	memberIdvalue :=l.ctx.Value()

	return
}
