package member

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMemberStatLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取统计信息
func NewGetMemberStatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberStatLogic {
	return &GetMemberStatLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMemberStatLogic) GetMemberStat(req *types.GetMemberStatReq) (resp *types.GetMemberStatResp, err error) {
	// todo: add your logic here and delete this line

	return
}
